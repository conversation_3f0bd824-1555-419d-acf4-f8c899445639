package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

// 新生成的游戏项目
// 游戏ID: 400150
// 游戏名称: LuckyBarrelTavern
// 作者: noir
// 生成时间: 2025-09-01 17:05:33

// 配置结构体
type c400150 struct {
	MaxPayout   int64              `yaml:"maxPayout"`   // 最大赔付
	Row         int                `yaml:"row"`         // 行数
	Column      int                `yaml:"column"`      // 列数
	Pattern     [][]basic.Position `yaml:"pattern"`     // 连线模式
	PayoutTable map[int16][]int64  `yaml:"payoutTable"` // 赔付表
	IconWeight  map[int16]int32    `yaml:"iconWeight"`  // 图标权重
	WildIcon    int16              `yaml:"wildIcon"`    // 百搭图标
	ScatterIcon int16              `yaml:"scatterIcon"` // 散布图标
	MinLimit    map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400150])

type m400150 struct {
	Config       c400150
	RandByWeight *utils.RandomWeightPicker[int16, int32]
}

func (m *m400150) Init(config []byte) {
	m.Config = utils.ParseYAML[c400150](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m m400150) ID() int32 {
	return 400150
}

func (m m400150) Line() int32 {
	return 20
}

func (m m400150) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400150) Exception(code int32) string {
	s := &games.S400150{}
	return s.Exception(code)
}

func (m *m400150) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400150) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	grid := m.generateGrid(rd)

	// 创建旋转结果
	spin := &games.S400150{
		Grid: grid,
		Pays: 0,
	}

	// 计算赔付
	m.calculatePayout(spin)

	return spin
}

func (m *m400150) generateGrid(rd *rand.Rand) []int16 {
	gridSize := m.Config.Row * m.Config.Column
	grid := make([]int16, gridSize)

	for i := 0; i < gridSize; i++ {
		grid[i] = m.RandByWeight.One(rd)
	}

	return grid
}

func (m *m400150) calculatePayout(spin *games.S400150) {
	// TODO: 实现赔付计算逻辑
	// 这里需要根据具体游戏规则实现
	spin.Pays = 0
}

func (m *m400150) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400150)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400150) Rule(ctx map[string]any) string {
	ruleData := map[string]any{
		"analInfo": map[string]any{
			"VIP_maxWinFreq_big":   5322317,
			"VIP_maxWinFreq_small": 180265,
			"arrlimits_winLimitK":  []int{5000, 12000},
			"baseReels":            []any{[]int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"freeReels":        []any{[]int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}, []int{9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12}},
			"lineStyles":       nil,
			"maxIteration":     10,
			"maxWinFreq_big":   81789076,
			"maxWinFreq_small": 300558,
			"minScatters":      []int{4, 4},
			"nSmallPicts":      13,
			"n_sectors":        20,
			"nhot_sectors":     []int{1, 4, 10},
			"outRates_vipmode": 96.22,
			"sasAdditionalId":  "HOB",
			"sasPaytableId":    "HOB960",
			"scatterIds":       []int{13},
			"statTablo": map[string]any{
				"bigwin":     10,
				"bonus":      5,
				"epicwin":    10,
				"rtp":        96.14,
				"show":       1,
				"volatility": 10,
			},
			"symbolNames": []string{"senior", "middle", "junior", "young", "chicken", "lobster", "meat", "fish", "egg", "hearts", "spades", "diamonds", "clubs", "scatter", "hearts", "hearts", "2x", "3x", "4x", "5x", "6x", "8x", "10x", "12x", "15x", "20x", "25x", "50x", "100x", "250x", "spades", "2x", "2x", "2x", "2x", "2x", "2x"},
			"volatility":  4,
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"bbLimitsWinK":    []int{12000, 12000, 12000, 12000, 12000},
		"betAssortment":   []int{1, 2, 3, 4, 5, 6, 7, 10, 15, 20, 25, 30, 35, 40, 45, 50, 60, 75, 100},
		"betPerGame":      20,
		"betPerLine":      1,
		"buyBonus": map[string]any{
			"buyTotalBetK": []map[string]any{map[string]any{
				"cost":    100,
				"id":      0,
				"prefix2": "_BASE_FG",
				"rtp":     96.24,
			}, map[string]any{
				"cost":    600,
				"id":      1,
				"prefix2": "_BASE_FG_HOT",
				"rtp":     96.28,
			}, map[string]any{
				"cost":    125,
				"id":      2,
				"prefix2": "_BASE_FG_MIX_0_20",
				"rtp":     96.25,
			}, map[string]any{
				"cost":    200,
				"id":      3,
				"prefix2": "_BASE_FG_MIX_1_5",
				"rtp":     96.26,
			}, map[string]any{
				"cost":    350,
				"id":      4,
				"prefix2": "_BASE_FG_MIX_2_2",
				"rtp":     96.27,
			}},
			"selectId": -1,
			"wasBuy":   0,
		},
		"denomAssortment_cents":  []int{1},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"fMult":                  1,
		"flyBarrels":             []any{},
		"gamegroup":              "base",
		"gcurrency":              "FUN",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{[]any{"off", 0, 0}},
			"fg": map[string]any{
				"firstAward":   10,
				"limit":        100,
				"nestingAward": 5,
				"portions":     5,
			},
			"paytable": []any{[]any{[]int{0, 256}, []int{12, 1000}, []int{10, 500}, []int{8, 200}}, []any{[]int{1, 256}, []int{12, 500}, []int{10, 200}, []int{8, 50}}, []any{[]int{2, 256}, []int{12, 300}, []int{10, 100}, []int{8, 40}}, []any{[]int{3, 256}, []int{12, 240}, []int{10, 40}, []int{8, 30}}, []any{[]int{4, 256}, []int{12, 200}, []int{10, 30}, []int{8, 20}}, []any{[]int{5, 256}, []int{12, 160}, []int{10, 25}, []int{8, 16}}, []any{[]int{6, 256}, []int{12, 100}, []int{10, 20}, []int{8, 10}}, []any{[]int{7, 256}, []int{12, 80}, []int{10, 18}, []int{8, 8}}, []any{[]int{8, 256}, []int{12, 40}, []int{10, 15}, []int{8, 5}}, []any{[]int{9, 256}, []int{12, 10}, []int{10, 5}, []int{8, 1}}, []any{[]int{10, 256}, []int{12, 10}, []int{10, 5}, []int{8, 1}}, []any{[]int{11, 256}, []int{12, 10}, []int{10, 5}, []int{8, 1}}, []any{[]int{12, 256}, []int{12, 10}, []int{10, 5}, []int{8, 1}}, []any{[]int{13, 8}}, []any{[]int{14, 16}}, []any{[]int{15, 16}}, []any{[]int{16, 128}, []int{1, 2}}, []any{[]int{17, 128}, []int{1, 3}}, []any{[]int{18, 128}, []int{1, 4}}, []any{[]int{19, 128}, []int{1, 5}}, []any{[]int{20, 128}, []int{1, 6}}, []any{[]int{21, 128}, []int{1, 8}}, []any{[]int{22, 128}, []int{1, 10}}, []any{[]int{23, 128}, []int{1, 12}}, []any{[]int{24, 128}, []int{1, 15}}, []any{[]int{25, 128}, []int{1, 20}}, []any{[]int{26, 128}, []int{1, 25}}, []any{[]int{27, 128}, []int{1, 50}}, []any{[]int{28, 128}, []int{1, 100}}, []any{[]int{29, 128}, []int{1, 250}}, []any{[]int{30, 32}}},
		},
		"helpseed":                true,
		"iBarrel":                 []int{0},
		"iBarrelPos":              []int{0},
		"incutId":                 9,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"linesAssortment":         []int{20},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   2000,
		"minBetPerGame_cents":     nil,
		"nlines":                  20,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               20,
		"present":                 "no",
		"reelstate":               0,
		"sMult":                   1,
		"setVip_inFreeSpinAlways": -1,
		"startBox":                []any{[]int{8, 1, 2, 13, 1, 10}, []int{8, 12, 13, 11, 12, 11}, []int{8, 9, 9, 10, 12, 9}, []int{1, 13, 9, 1, 12, 2}, []int{1, 11, 9, 1, 9, 2}},
		"stopBox":                 []any{[]int{8, 1, 2, 13, 1, 10}, []int{8, 12, 13, 11, 12, 11}, []int{8, 9, 9, 10, 12, 9}, []int{1, 13, 9, 1, 12, 2}, []int{1, 11, 9, 1, 9, 2}},
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.25,
			"vip_noSpecSeed": true,
			"wasBuyVip":      0,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         12000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 12000,
		},
	}

	b, _ := json.Marshal(ruleData)
	return string(b)
}

func (m m400150) InputCoef(ctl int32) int32 {
	return 100
}

func (m m400150) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
