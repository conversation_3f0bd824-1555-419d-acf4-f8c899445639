package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

// slot
// 游戏ID: 400156
// 游戏名称: TortugaCodeX
// 作者: noir
// 生成时间: 2025-08-11 14:19:52

// 配置结构体
type c400156 struct {
	MaxPayout        int64              `yaml:"maxPayout"`        // 最大赔付
	Line             int32              `yaml:"line"`             // 线数
	Row              int                `yaml:"row"`              // 行数
	Column           int                `yaml:"column"`           // 列数
	Pattern          [][]basic.Position `yaml:"pattern"`          // 连线模式
	PayoutTable      map[int16][]int64  `yaml:"payoutTable"`      // 赔付表
	IconWeight       map[int16]int32    `yaml:"iconWeight"`       // 图标权重
	DopWeight        map[int16]int32    `yaml:"dopWeight"`        // Dop权重
	BoxWeight        map[int16]int32    `yaml:"boxWeight"`        // Box权重
	SubGameBoxWeight map[int16]int32    `yaml:"subGameBoxWeight"` // 子游戏BOX权重
	WildIcon         int16              `yaml:"wildIcon"`         // 百搭图标
	ScatterIcon      int16              `yaml:"scatterIcon"`      // 散布图标
	MinLimit         map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400156])

type m400156 struct {
	Config                     c400156
	RandByWeight               *utils.RandomWeightPicker[int16, int32]
	RandByWeightWithoutScatter *utils.RandomWeightPicker[int16, int32]
	RandByDopWeight            *utils.RandomWeightPicker[int16, int32]
	RandBySubGameBoxWeight     *utils.RandomWeightPicker[int16, int32]
	RandByDopWithoutMan        *utils.RandomWeightPicker[int16, int32]
	RandByDopWithoutWild       *utils.RandomWeightPicker[int16, int32]
	RandByBoxWeight            *utils.RandomWeightPicker[int16, int32]
}

func (m *m400156) Init(config []byte) {
	m.Config = utils.ParseYAML[c400156](config)
	withoutScatter := utils.FilterIconWeight(m.Config.IconWeight, func(icon int16) bool {
		return icon != m.Config.ScatterIcon
	})

	withoutWild := utils.FilterIconWeight(m.Config.DopWeight, func(icon int16) bool {
		return icon != m.Config.WildIcon
	})

	withoutMan := utils.FilterIconWeight(m.Config.DopWeight, func(icon int16) bool {
		return icon != 26 && icon != 27 && icon != 28
	})

	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	m.RandByWeightWithoutScatter = utils.NewRandomWeightPicker(withoutScatter)
	m.RandByDopWeight = utils.NewRandomWeightPicker(m.Config.DopWeight)
	m.RandBySubGameBoxWeight = utils.NewRandomWeightPicker(m.Config.SubGameBoxWeight)
	m.RandByDopWithoutMan = utils.NewRandomWeightPicker(withoutMan)
	m.RandByDopWithoutWild = utils.NewRandomWeightPicker(withoutWild)
	m.RandByBoxWeight = utils.NewRandomWeightPicker(m.Config.BoxWeight)
}

func (m m400156) ID() int32 {
	return 400156
}

func (m m400156) Line() int32 {
	return 25
}

func (m m400156) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400156) Exception(code int32) string {
	s := &games.S400156{}
	return s.Exception(code)
}

func (m *m400156) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400156) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	grid := m.generateGrid(rd)

	// 创建旋转结果
	spin := &games.S400156{
		Grid:           grid,
		Line:           m.Line(),
		Pays:           0,
		CoinMul:        0,
		BoxMul:         0,
		SubGameInfoMul: 0,
		SubGameInfo:    []map[string]any{},
	}
	spin.Dop = m.genereateDop(rd)
	spin.Box, spin.Dop = m.procesScatterWithDop(rd, spin.Grid, spin.Dop, spin)
	spin.Lines = m.calculatePayout(spin.Box, spin)

	upNums := make([]int16, m.Config.Column)
	copy(upNums, spin.Dop.UpNums)
	if spin.IsSpec {
		spin.SubGameInfo = append(spin.SubGameInfo, m.genereateSubGameInfo(rd, 2, upNums, spin))
	} else if spin.IsHot {
		for i := range upNums {
			upNums[i] += 3
			if upNums[i] >= 9 {
				upNums[i] = 9
			}
		}
		spin.SubGameInfo = append(spin.SubGameInfo, m.genereateSubGameInfo(rd, 2, upNums, spin))
	}

	if spin.BoxMul != 0 || spin.CoinMul != 0 || spin.SubGameInfoMul != 0 {
		spin.Pays += int32(spin.BoxMul+spin.CoinMul+spin.SubGameInfoMul) * int32(m.Line())
	}
	return spin
}

func (m *m400156) generateGrid(rd *rand.Rand) []int16 {
	gridSize := m.Config.Row * m.Config.Column
	grid := make([]int16, gridSize)

	scatterInCols := map[int]int{0: 0, 1: 0, 2: 0, 3: 0, 4: 0}

	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col

			if scatterInCols[col] >= 1 {
				grid[index] = m.RandByWeightWithoutScatter.One(rd)
			} else {
				grid[index] = m.RandByWeight.One(rd)

				if grid[index] == m.Config.ScatterIcon {
					scatterInCols[col]++
				}
			}
		}
	}
	return grid
}

func (m *m400156) genereateDop(rd *rand.Rand) games.Dop400156 {
	var upBox, upBoxView [][]int16
	box := m.RandByDopWeight.More(m.Config.Column, rd)

	replaceIconCount := 0
	wildIconCount := 0
	for _, v := range box {
		upBox = append(upBox, []int16{v})
		upBoxView = append(upBoxView, []int16{v})

		if replaceIconCount >= 1 || wildIconCount >= 1 {
			v = m.RandByDopWithoutMan.One(rd)
		}
		if wildIconCount >= 1 {
			v = m.RandByDopWithoutWild.One(rd)
		}
		if v > 25 && v < 29 {
			replaceIconCount++
		}
		if v == 25 {
			wildIconCount++
		}

	}
	dop := games.Dop400156{
		UpBox:     upBox,
		UpBoxView: upBoxView,
	}
	return dop
}

func (m *m400156) procesScatterWithDop(rd *rand.Rand, grid []int16, dop games.Dop400156, spin *games.S400156) ([]int16, games.Dop400156) {
	scatterInCols := map[int]int{0: 0, 1: 0, 2: 0, 3: 0, 4: 0}
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if grid[index] == m.Config.ScatterIcon {
				scatterInCols[col]++
			}
		}
	}

	var replaceIcon int16
	box := make([]int16, len(grid))
	copy(box, grid)
	replaceMap := map[int16]int{}

	for i := m.Config.Column - 1; i >= 0; i-- {
		if scatterInCols[m.Config.Column-i-1] >= 1 {
			if dop.UpBoxView[i][0] > 24 && dop.UpBoxView[i][0] < 29 {
				switch dop.UpBoxView[i][0] {
				case 25:
					replaceIcon = 0
				case 26:
					replaceIcon = 2
				case 27:
					replaceIcon = 3
				case 28:
					replaceIcon = 4
				}

				for i, ico := range grid {
					if ico > 8 {
						replaceMap[box[i]]++
						box[i] = replaceIcon
					}
				}

				replaceList := []int16{}
				for k := range replaceMap {
					replaceList = append(replaceList, k)
				}
				dop.Boost = append(dop.Boost, replaceList)
			} else if dop.UpBoxView[i][0] == 29 {
				spin.SubGameInfo = append(spin.SubGameInfo, m.genereateSubGameInfo(rd, 0, nil, spin))
				dop.Boost = append(dop.Boost, spin.BoxMul)
			} else if dop.UpBoxView[i][0] == 30 {
				spin.SubGameInfo = append(spin.SubGameInfo, m.genereateSubGameInfo(rd, 1, nil, spin))
				dop.Boost = append(dop.Boost, spin.CoinMul)
			} else if dop.UpBoxView[i][0] == 25 {
				replaceList := []int{}
				for i := range grid {
					if rd.Intn(100) < 50 {
						if grid[i] == m.Config.ScatterIcon {
							continue
						}
						replaceList = append(replaceList, i)
					}
				}
				utils.ShuffleIcons(replaceList, rd)
				for _, i := range replaceList {
					spin.Box[i] = m.Config.WildIcon
				}

				dop.Boost = append(dop.Boost, replaceList)
			} else {
				dop.Boost = append(dop.Boost, nil)
			}
		} else {
			dop.Boost = append(dop.Boost, nil)
		}
		if dop.UpBox[i][0] >= 25 {
			switch dop.UpBox[i][0] {
			case 25:
				dop.UpBox[i] = []int16{24}
			case 26:
				dop.UpBox[i] = []int16{23}
			case 27:
				dop.UpBox[i] = []int16{22}
			case 28:
				dop.UpBox[i] = []int16{21}
			case 29:
				dop.UpBox[i] = []int16{20}
			case 30:
				dop.UpBox[i] = []int16{19}
			}
		}
		dop.UpNums = append(dop.UpNums, int16(dop.UpBox[i][0]-15))
	}

	return box, dop
}

func (m *m400156) genereateSubGameInfo(rd *rand.Rand, subGameType int, dop []int16, spin *games.S400156) map[string]any {
	switch subGameType {
	case 0:
		mul := int(m.RandByBoxWeight.One(rd))
		spin.BoxMul = mul
		subGameInfo := map[string]any{
			"category":      "Bonus0",
			"type":          "0",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        mul * int(m.Line()),
			"paidWin":       mul * int(m.Line()),
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "inBonus0",
			"add":           map[string]any{},
			"onlyToBD":      nil,
		}
		return subGameInfo
	case 1:
		mul := 10
		spin.CoinMul = mul
		subGameInfo := map[string]any{
			"category":      "Bonus1",
			"type":          "1",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        mul * int(m.Line()),
			"paidWin":       mul * int(m.Line()),
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "inBonus1",
			"add":           map[string]any{},
			"onlyToBD":      nil,
		}
		return subGameInfo
	case 2:
		reel := []int16{}
		box := m.RandBySubGameBoxWeight.More(m.Config.Row*m.Config.Column, rd)
		for i := 0; i < len(box); i += 5 {
			if box[i : i+5][0] != 2 {
				reel = append(reel, box[i : i+5][0])
			} else {
				reel = append(reel, box[i : i+5][0])
				break
			}
		}

		for i := 0; i < len(reel); i++ {
			var sum int16 = 0
			switch reel[i] {
			case 0:
				sum = dop[i] + dop[i+1]
			case 1:
				sum = dop[i] * dop[i+1]
			case 2:
				if i != 0 {
					sum = dop[len(reel)-1]
				}
			}
			dop[i+1] = sum
		}
		mul := dop[len(reel)]
		spin.SubGameInfoMul = int(mul)

		fakeBox := []int16{}
		for i := 0; i < len(box); i += 5 {
			if i < len(reel) {
				fakeBox = append(fakeBox, box[i+1:i+5]...)
			} else {
				fakeBox = append(fakeBox, box[i:i+5]...)
			}
		}
		subGameInfo := map[string]any{
			"category":      "Bonus2",
			"type":          "2",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        int(mul) * int(m.Line()),
			"paidWin":       -1,
			"attempt":       0,
			"av":            []int{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "",
			"add":           map[string]any{},
			"onlyToBD":      nil,
			"sendRestore":   nil,
			"userChoice":    "",
			"box":           box,
			"real":          reel,
			"fake":          fakeBox,
		}
		return subGameInfo
	}
	return nil
}

func (m *m400156) calculatePayout(grid []int16, spin *games.S400156) []belatra.LinesInfo {
	scatterIcon := 0
	linesInfo := []belatra.LinesInfo{}

	gridRows := make([][]int16, m.Config.Row)

	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			val := grid[index]
			gridRows[row][col] = val

			if val == m.Config.ScatterIcon {
				scatterIcon += 1
			}
		}
	}

	for lineID, pattern := range m.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			symbols[i] = gridRows[row][col]
		}

		if ok, mul, payout, online := m.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   payout,
					K:      mul,
					Win:    payout * int64(mul),
					OnLine: online,
				},
			})
			spin.Pays += int32(payout * int64(mul))
		}
	}
	if scatterIcon >= 3 && scatterIcon < 5 {
		spin.IsSpec = true
	} else if scatterIcon == 5 {
		spin.IsHot = true
	}

	return linesInfo
}

func (m m400156) checkLine(symbols []int16) (bool, int, int64, []int16) {
	if len(symbols) < 3 {
		return false, 1, 0, nil
	}
	firstIcon := symbols[0]
	count := 0
	mul := 0

	if firstIcon == m.Config.ScatterIcon {
		return false, 1, 0, nil
	}

	// 如果第一个图标是Wild
	if firstIcon == m.Config.WildIcon {
		actualIcon := m.Config.WildIcon
		for _, icon := range symbols {
			if icon != m.Config.WildIcon && icon != m.Config.ScatterIcon {
				actualIcon = icon
				break
			}
		}

		if actualIcon == m.Config.WildIcon {
			for _, icon := range symbols {
				if icon == m.Config.WildIcon {
					count++
				} else if icon == m.Config.ScatterIcon {
					break
				} else {
					break
				}
			}
		} else {
			for _, icon := range symbols {
				if icon == actualIcon || icon == m.Config.WildIcon {
					count++
				} else if icon == m.Config.ScatterIcon {
					break
				} else {
					break
				}
			}
			firstIcon = actualIcon
		}
	} else {
		for _, icon := range symbols {
			if icon == firstIcon || icon == m.Config.WildIcon {
				count++
			} else {
				break
			}
		}
	}

	if count >= 3 {
		payout := m.Config.PayoutTable[firstIcon][count]
		online := []int16{-1, -1, -1, -1, -1}

		copy(online, symbols[:count])
		for i := 0; i < len(symbols); i++ {
			if online[i] == -1 {
				online[i] = 127
			}
		}

		if mul == 0 {
			mul = 1
		}
		return true, mul, payout, online
	}
	return false, 1, 0, nil
}

func (m *m400156) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400156)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400156) Rule(ctx map[string]any) string {
	ruleData := map[string]any{
		"analInfo": map[string]any{
			"arrlimits_winLimitK": []int{59259},
			"baseIncuts":          []int{2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13},
			"baseReels":           []any{[]int{0, 9, 9, 9, 9, 2, 2, 10, 10, 10, 1, 5, 5, 12, 12, 12, 12, 3, 3, 11, 11, 11, 11, 7, 7, 9, 9, 9, 8, 8, 0, 11, 11, 11, 1, 7, 13, 13, 13, 13, 8, 12, 12, 12, 12, 4, 4, 13, 13, 13, 6, 6, 1, 9, 9, 9, 9, 9, 2, 2, 10, 10, 10, 5, 12, 12, 12, 12, 3, 3, 11, 11, 11, 11, 7, 7, 9, 9, 9, 8, 8, 11, 11, 11, 7, 13, 13, 13, 13, 8, 12, 12, 12, 12, 4, 4, 13, 13, 13, 6, 6}, []int{0, 7, 7, 9, 9, 3, 11, 11, 11, 1, 6, 9, 9, 5, 5, 13, 13, 13, 13, 2, 2, 9, 9, 8, 8, 11, 11, 11, 5, 12, 12, 12, 6, 6, 10, 10, 10, 10, 4, 4, 12, 12, 12, 1, 8, 8, 9, 9, 5, 10, 10, 0, 7, 7, 9, 9, 3, 3, 1, 11, 11, 11, 6, 9, 9, 5, 5, 13, 13, 13, 13, 2, 2, 9, 9, 8, 8, 11, 11, 11, 5, 12, 12, 12, 6, 6, 10, 10, 10, 10, 4, 4, 12, 12, 12, 8, 8, 9, 9, 5, 10, 10}, []int{0, 11, 11, 11, 11, 3, 3, 12, 12, 1, 13, 6, 4, 4, 12, 12, 9, 9, 2, 2, 13, 13, 13, 13, 6, 6, 9, 7, 7, 1, 10, 10, 10, 10, 5, 5, 9, 9, 9, 8, 8, 11, 11, 11, 8, 1, 10, 10, 10, 10, 3, 9, 0, 11, 11, 11, 11, 3, 3, 12, 12, 13, 6, 4, 4, 1, 12, 12, 9, 9, 2, 2, 13, 13, 13, 13, 6, 6, 9, 7, 7, 10, 10, 10, 10, 5, 5, 9, 9, 9, 8, 8, 1, 11, 11, 11, 8, 10, 10, 10, 10, 3, 9}, []int{0, 8, 8, 12, 12, 2, 2, 13, 13, 1, 13, 6, 10, 10, 10, 10, 3, 3, 11, 11, 13, 13, 5, 5, 12, 12, 12, 1, 4, 4, 9, 9, 9, 9, 7, 7, 9, 9, 9, 2, 11, 11, 11, 11, 3, 10, 10, 10, 8, 8, 11, 0, 8, 8, 12, 12, 2, 2, 1, 13, 13, 13, 6, 10, 10, 10, 10, 3, 3, 11, 11, 13, 13, 5, 5, 12, 12, 12, 1, 4, 4, 9, 9, 9, 9, 7, 7, 9, 9, 9, 2, 11, 11, 11, 11, 3, 1, 10, 10, 10, 8, 8, 11}, []int{0, 13, 13, 13, 13, 3, 3, 9, 9, 1, 9, 6, 6, 10, 10, 10, 10, 2, 2, 12, 12, 12, 12, 8, 1, 11, 11, 11, 11, 4, 4, 9, 9, 9, 9, 5, 5, 9, 9, 1, 10, 10, 10, 10, 7, 7, 12, 12, 12, 8, 13, 13, 0, 13, 13, 13, 13, 1, 3, 3, 9, 9, 9, 6, 6, 10, 10, 10, 10, 2, 2, 1, 12, 12, 12, 12, 8, 11, 11, 11, 11, 4, 4, 9, 9, 9, 9, 1, 5, 5, 9, 9, 10, 10, 10, 10, 7, 7, 1, 12, 12, 12, 8, 13, 13}},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"incutIds":         []int{14},
			"lineStyles":       []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{3, 2, 1, 2, 3}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}, []int{0, 1, 1, 1, 0}, []int{3, 2, 2, 2, 3}, []int{2, 3, 3, 3, 2}, []int{1, 0, 0, 0, 1}, []int{2, 1, 2, 1, 2}, []int{1, 2, 1, 2, 1}, []int{1, 2, 2, 2, 1}, []int{1, 1, 2, 1, 1}, []int{2, 2, 1, 2, 2}},
			"minScatters":      []int{3},
			"outRates_vipmode": 96.08,
			"sasAdditionalId":  "TOC",
			"sasPaytableId":    "TOC966",
			"scatterIds":       []int{1},
			"statTablo": map[string]any{
				"bigwin":     7,
				"bonus":      7,
				"epicwin":    5,
				"rtp":        96.06,
				"show":       1,
				"volatility": 9,
			},
			"symbolNames": []string{"wild", "scatter", "pirate_bob", "pirate_patrick", "girl", "box", "rum", "pistols", "coins", "a", "k", "q", "j", "ten", "ten", "up_zero", "up_one", "up_two", "up_three", "up_four", "up_five", "up_six", "up_seven", "up_eight", "up_nine", "wild", "pirate_bob", "pirate_patrick", "girl", "up_box", "up_coin"},
			"upReels":     []any{[]int{16, 17, 18, 19, 20, 21, 25, 22, 23, 24, 16, 17, 18, 19, 20, 30, 21, 22, 23, 24, 16, 17, 18, 19, 20, 26, 21, 22, 23, 24, 16, 17, 18, 19, 20, 21, 22, 23, 24, 16, 17, 18, 19, 20, 21, 22, 23, 24, 27, 23, 24, 16, 17, 18, 29, 19, 20, 21, 22, 23, 24, 16, 17, 18, 19, 20, 21, 22, 23, 24, 28}},
			"volatility":  4,
			"wildIds":     []int{0},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"betAssortment":   []int{1, 2, 3, 4, 5, 6, 8, 10, 12, 14, 16},
		"betPerGame":      ctx["input"],
		"betPerLine":      ctx["betPerLine"],
		"boxValues":       []int{5, 10, 15, 20, 25, 40, 50, 75, 100, 500, 1000},
		"buyBonus": map[string]any{
			"buyTotalBetK": []map[string]any{map[string]any{
				"cost":    100,
				"id":      0,
				"prefix2": "_BASE_SC_3",
				"rtp":     96.11,
			}, map[string]any{
				"cost":    1000,
				"id":      1,
				"prefix2": "_BASE_SC_5",
				"rtp":     96.14,
			}},
			"selectId": -1,
			"wasBuy":   0,
		},
		"denomAssortment_cents": []int{1},
		"dop": map[string]any{
			"boost":     []any{},
			"upBox":     []any{[]int{16}, []int{25}, []int{23}, []int{23}, []int{25}},
			"upBoxView": []any{[]int{16}, []int{25}, []int{23}, []int{23}, []int{25}},
			"upNums":    []int{9, 8, 8, 9, 1},
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles":  []any{[]any{"off", 0, 0}},
			"paytable": []any{[]any{[]int{0, 1}, []int{5, 300}, []int{4, 150}, []int{3, 75}}, []any{[]int{1, 8}}, []any{[]int{2, 4}, []int{5, 250}, []int{4, 100}, []int{3, 60}}, []any{[]int{3, 4}, []int{5, 200}, []int{4, 90}, []int{3, 50}}, []any{[]int{4, 4}, []int{5, 150}, []int{4, 75}, []int{3, 40}}, []any{[]int{5, 4}, []int{5, 125}, []int{4, 60}, []int{3, 25}}, []any{[]int{6, 4}, []int{5, 125}, []int{4, 60}, []int{3, 25}}, []any{[]int{7, 4}, []int{5, 100}, []int{4, 50}, []int{3, 20}}, []any{[]int{8, 4}, []int{5, 100}, []int{4, 50}, []int{3, 20}}, []any{[]int{9, 4}, []int{5, 75}, []int{4, 30}, []int{3, 15}}, []any{[]int{10, 4}, []int{5, 50}, []int{4, 25}, []int{3, 10}}, []any{[]int{11, 4}, []int{5, 50}, []int{4, 25}, []int{3, 10}}, []any{[]int{12, 4}, []int{5, 40}, []int{4, 20}, []int{3, 5}}, []any{[]int{13, 4}, []int{5, 40}, []int{4, 20}, []int{3, 5}}, []any{[]int{14, 16}}, []any{[]int{15, 32}}, []any{[]int{16, 32}}, []any{[]int{17, 32}}, []any{[]int{18, 32}}, []any{[]int{19, 32}}, []any{[]int{20, 32}}, []any{[]int{21, 32}}, []any{[]int{22, 32}}, []any{[]int{23, 32}}, []any{[]int{24, 32}}},
		},
		"helpseed":                true,
		"incutId":                 3,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"linesAssortment":         []int{25},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   20000000,
		"minBetPerGame_cents":     nil,
		"nlines":                  25,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               ctx["input"],
		"present":                 "no",
		"reelstate":               0,
		"setVip_inFreeSpinAlways": -1,
		"startBox":                []any{[]int{12, 8, 9, 3, 11}, []int{12, 8, 7, 4, 11}, []int{3, 11, 7, 4, 7}, []int{11, 11, 3, 6, 12}},
		"stopBox":                 []any{[]int{12, 8, 9, 3, 11}, []int{12, 8, 7, 4, 11}, []int{3, 11, 7, 4, 7}, []int{11, 11, 3, 6, 12}},
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.6,
			"vip_noSpecSeed": true,
			"wasBuyVip":      0,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         59259,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 59259,
		},
	}

	b, _ := json.Marshal(ruleData)
	return string(b)
}

func (m m400156) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	case 2:
		return 100000
	case 3:
		return 160
	default:
		return 100
	}
}

func (m m400156) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
