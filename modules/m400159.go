package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

// oneGame
// 游戏ID: 400159
// 游戏名称: MakeItGold
// 作者: pasty
// 生成时间: 2025-08-27 15:00:39

// 配置结构体
type c400159 struct {
	MaxPayout   int64              `yaml:"maxPayout"`   // 最大赔付
	Line        int32              `yaml:"line"`        // 线数
	InitRow     int                `yaml:"initRow"`     // 初始行数
	MaxRow      int                `yaml:"maxRow"`      // 最大行数
	Column      int                `yaml:"column"`      // 列数
	Pattern     [][]basic.Position `yaml:"pattern"`     // 连线模式
	PayoutTable map[int16][]int64  `yaml:"payoutTable"` // 赔付表
	IconWeight  map[int16]int32    `yaml:"iconWeight"`  // 图标权重
	WildIcon    int16              `yaml:"wildIcon"`    // 百搭图标
	ScatterIcon int16              `yaml:"scatterIcon"` // 散布图标
	MinLimit    map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400159])

type m400159 struct {
	Config             c400159
	RandByWeight       *utils.RandomWeightPicker[int16, int32]
	RandByNoWildWeight *utils.RandomWeightPicker[int16, int32]
}

func (m *m400159) Init(config []byte) {
	m.Config = utils.ParseYAML[c400159](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	noWild := make(map[int16]int32)
	for key, weight := range m.Config.IconWeight {
		if key != m.Config.WildIcon {
			noWild[key] = weight
		}
	}
	m.RandByNoWildWeight = utils.NewRandomWeightPicker(noWild)
}

func (m m400159) ID() int32 {
	return 400159
}

func (m m400159) Line() int32 {
	return 20
}

func (m m400159) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400159) Exception(code int32) string {
	s := &games.S400159{}
	return s.Exception(code)
}

func (m *m400159) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400159) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	//page := m.generatePage(rd)

	// 创建旋转结果
	spin := &games.S400159{
		Pays: 0,
	}
	if spin.Golden {
		//todo
	}
	page := m.generatePage(rd)
	spin.Pays = page.Pays
	spin.Pages = append(spin.Pages, page)
	scatter := 0
	for i := 0; i < len(page.StopGrid); i++ {
		for j := 0; j < m.Config.Column; j++ {
			if page.StopGrid[i][j] == m.Config.ScatterIcon {
				scatter++
			}
		}
	}
	if scatter >= 4 && scatter < 6 {
		spin.IsFree = true
	}
	if scatter >= 6 {
		spin.IsFree = false
		spin.IsHot = true
	}
	if spin.IsFree || spin.IsHot {
		addSpin := scatter - 4
		addSpin *= 2
		TotalSpin := 8 + addSpin
		for i := 0; i < TotalSpin; i++ {
			page := m.generatePage(rd)
			spin.Pays = page.Pays
			spin.Pages = append(spin.Pages, page)
		}
	}

	// 计算赔付

	return spin
}

func (m *m400159) generatePage(rd *rand.Rand) games.P400159 {
	stopGrid, iterInfo, wins := m.genIterInfo(rd)
	if len(wins) == 0 {
		return games.P400159{
			StopGrid: stopGrid,
			Pays:     0,
		}
	}
	return games.P400159{
		Pays:      iterInfo[len(iterInfo)-1].Curwin,
		IterInfo:  iterInfo,
		StopGrid:  iterInfo[len(iterInfo)-1].Box,
		LResGrid:  iterInfo[len(iterInfo)-1].Resbox,
		LMarkGrid: iterInfo[len(iterInfo)-1].Markbox,
		Wins:      wins,
	}
}

func (m *m400159) genIterInfo(rd *rand.Rand) ([][]int16, []belatra.IterInfo, []games.Win400159) {
	row := 4
	wins := []games.Win400159{}
	iterInfo := []belatra.IterInfo{}
	icons := m.RandByWeight.More(row*m.Config.Column, rd)
	stopGrid := make([][]int16, row)
	for k := 0; k < 4; k++ {
		stopGrid[k] = make([]int16, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			if j == 0 {
				if icons[k*m.Config.Column+j] == m.Config.WildIcon {
					stopGrid[k][j] = m.RandByNoWildWeight.One(rd)
				} else {
					stopGrid[k][j] = icons[k*m.Config.Column+j]
				}
			} else {
				stopGrid[k][j] = icons[k*m.Config.Column+j]
			}
		}
	}
	wins = m.calculatePayout(stopGrid)
	allwin := int32(0)
	if len(wins) > 0 {
		win := int32(0)
		for _, i := range wins {
			win += int32(i.Win)
		}
		allwin += win
		winMaskGrid, newPosGrid, dropShiftGrid := m.genNormAllGrid(stopGrid, row, wins)
		acts := m.genActs(stopGrid, winMaskGrid, wins)
		iterInfo = append(iterInfo, belatra.IterInfo{
			Hi:            row,
			Box:           stopGrid,
			Mult:          1,
			Win:           win,
			WinWomult:     win,
			Order:         []int16{1, 2, 3, 4, 5, 6},
			Resbox:        stopGrid,
			Markbox:       stopGrid,
			Winmask:       winMaskGrid,
			Acts:          acts,
			StartStorageN: 0,
			NwildmultOld:  0,
			NwildmultNew:  0,
			Curwin:        allwin,
			Newpos:        newPosGrid,
			DropShiftBox:  dropShiftGrid,
		})
		symbolCount := 0
		for {
			if row < 8 {
				row++
			}
			count := 0
			stopGrid = m.dropSymbols(rd, stopGrid, row, wins)
			wins = m.calculatePayout(stopGrid)
			win := int32(0)
			for _, i := range wins {
				win += int32(i.Win)
			}
			allwin += win
			winMaskGrid, newPosGrid, dropShiftGrid := m.genNormAllGrid(stopGrid, row, wins)
			acts := m.genActs(stopGrid, winMaskGrid, wins)
			iterInfo = append(iterInfo, belatra.IterInfo{
				Hi:            row,
				Box:           stopGrid,
				Mult:          1,
				Win:           win,
				WinWomult:     win,
				Order:         []int16{1, 2, 3, 4, 5, 6},
				Resbox:        stopGrid,
				Markbox:       stopGrid,
				Winmask:       winMaskGrid,
				Acts:          acts,
				StartStorageN: iterInfo[len(iterInfo)-2].StartStorageN + symbolCount,
				NwildmultOld:  0,
				NwildmultNew:  0,
				Curwin:        allwin,
				Newpos:        newPosGrid,
				DropShiftBox:  dropShiftGrid,
			})
			for _, i := range wins {
				count += i.Count
			}
			symbolCount = count
			if len(wins) == 0 {
				return stopGrid, iterInfo, wins
			}
		}
	} else {
		// todo free mode 需要调整
		iterInfo = append(iterInfo, belatra.IterInfo{
			Hi:        row,
			Box:       stopGrid,
			Mult:      1,
			Win:       0,
			WinWomult: 0,
			Order:     []int16{1, 2, 3, 4, 5, 6},
			Resbox:    stopGrid,
			Markbox: [][]int16{
				{0, 0, 0, 0, 0, 0},
				{0, 0, 0, 0, 0, 0},
				{0, 0, 0, 0, 0, 0},
				{0, 0, 0, 0, 0, 0},
			},
			Winmask: [][]int16{
				{0, 0, 0, 0, 0, 0},
				{0, 0, 0, 0, 0, 0},
				{0, 0, 0, 0, 0, 0},
				{0, 0, 0, 0, 0, 0},
			},
			Acts:          nil,
			StartStorageN: 0,
			NwildmultOld:  0,
			NwildmultNew:  0,
			Curwin:        allwin,
			Newpos:        nil,
			DropShiftBox:  nil,
		})
		return stopGrid, iterInfo, wins
	}
}

func (m *m400159) calculatePayout(grid [][]int16) []games.Win400159 {
	var wins []games.Win400159
	for symbol := int16(1); symbol < 13; symbol++ {
		var positions []games.Position400159

		line := 0
		symbolCount := 0
		for col := 0; col < m.Config.Column; col++ {
			count := 0
			for row := 0; row < len(grid); row++ {
				if grid[row][col] == symbol || grid[row][col] == m.Config.WildIcon { // Wild 可替代
					count++
					positions = append(positions, games.Position400159{Col: col, Row: row})
				}
			}
			if count > 0 {
				symbolCount += count
				line++
			} else {
				break // 断开则停止
			}
		}
		if line >= 3 {
			win := m.getPayout(symbol, symbolCount)
			wins = append(wins, games.Win400159{
				Icon:      symbol,
				Count:     symbolCount,
				Win:       int64(win),
				Positions: positions,
			})
		}
	}
	return wins
}

func (m *m400159) genActs(stopGrid [][]int16, winMaskGrid [][]int16, wins []games.Win400159) []belatra.Act {
	// todo 需要按照数据构建acts
	allwin := int64(0)
	for _, win := range wins {
		allwin += win.Win

	}
	acts := []belatra.Act{}
	acts = append(acts, belatra.Act{
		Name: "winnings",
		Data: map[string]interface{}{
			"lineswin":        allwin,
			"lineswin_womult": allwin,
			"winmask":         winMaskGrid,
			"markbox":         stopGrid,
			"resbox":          stopGrid,
		},
	})
	acts = append(acts, belatra.Act{
		Name: "to_storage",
		Data: map[string]interface{}{
			"oldn": 0,
			"newn": 0,
		},
	})
	acts = append(acts, belatra.Act{
		Name: "mult_modif",
		Data: map[string]interface{}{
			"newmult": 1,
		},
	})
	return acts
}

func (m *m400159) dropSymbols(rd *rand.Rand, stopGrid [][]int16, row int, wins []games.Win400159) [][]int16 {
	cleanGrid := m.cloneSlice400159(stopGrid)

	newStopGrid := m.makeGrid(row, m.Config.Column)
	for _, v := range wins {
		for _, pos := range v.Positions {
			cleanGrid[pos.Row][pos.Col] = -99
		}
	}
	for i := 0; i < m.Config.Column; i++ {
		var result []int16
		for j := row - 2; j > 0; j-- {
			if cleanGrid[j][i] != -99 {
				result = append(result, cleanGrid[j][i])
			}
		}
		// 从底部开始填充非-99的值，顶部填充-99
		fillIndex := row - 1 // 从最底部开始填充
		for _, val := range result {
			if fillIndex > 0 {
				newStopGrid[fillIndex][i] = val
				fillIndex--
			}
		}
		for j := 0; j < row; j++ {
			if newStopGrid[j][i] == -99 {
				newStopGrid[j][i] = m.RandByWeight.One(rd)
			}
		}
	}
	return newStopGrid
}

func (m *m400159) getPayout(symbol int16, count int) int32 {
	payoutTable := m.Config.PayoutTable[symbol]
	for index, payout := range payoutTable {
		if index == count {
			return int32(payout)
		}
		if index == 20 && count > index {
			return int32(payout)
		}
	}
	return 0
}

func (m *m400159) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400159)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.MaxRow
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400159) Rule(ctx map[string]any) string {
	gs := map[string]any{
		"USED_iterInfo": true,
		"analInfo": map[string]any{
			"VIP_maxWinFreq_big":   22806681,
			"VIP_maxWinFreq_small": 397311,
			"arrlimits_winLimitK":  []int{5000, 25000},
			"coeff_energy_win":     5,
			"coinsAssort":          []int{1, 2, 3, 5, 10, 15, 20, 50, 100, 1000},
			"coinsTape":            []any{[]int{21, 20, 19, 18, 21, 18, 20, 19, 21, 20, 20, 18, 18, 24, 19, 21, 20, 23, 21, 20, 20, 21, 19, 22, 23, 19, 24, 18, 19, 19, 21, 19, 19, 20, 19, 20, 19, 18, 21, 21, 21, 19, 19, 19, 19, 23, 18, 22, 22, 18, 19, 18, 21, 20, 21, 21, 18, 21, 20, 20, 20, 20, 24, 21, 20, 19}},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"maxIteration":     11,
			"maxWinFreq_big":   78443434,
			"maxWinFreq_small": 576640,
			"minScatters":      []int{4},
			"nSmallPicts":      13,
			"outRates_vipmode": 96.22,
			"sasAdditionalId":  "MGO",
			"sasPaytableId":    "MGO960",
			"scatterIds":       []int{13},
			"statTablo": map[string]any{
				"bigwin":     10,
				"bonus":      7,
				"epicwin":    10,
				"rtp":        96.13,
				"show":       1,
				"volatility": 9,
			},
			"symbolNames": []string{"wild", "book", "hourglass", "flask", "mortar", "wind", "water", "fire", "earth", "a", "k", "q", "j", "scatter", "stone", "stone", "wild", "stone", "coin_copper", "coin_copper", "coin_copper", "coin_copper", "coin_silver", "coin_silver", "coin_silver", "coin_silver", "coin_gold", "coin_gold"},
			"volatility":  4,
			"wildIds":     []int{0, 16},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"bbLimitsWinK":    []int{25000, 25000, 25000, 25000},
		"betAssortment":   ctx["betAssortment"],
		"betPerGame":      ctx["input"],
		"betPerLine":      ctx["betPerLine"],
		"buyBonus": map[string]any{
			"buyTotalBetK": []map[string]any{{
				"cost":    100,
				"id":      0,
				"prefix2": "_BASE_FG",
				"rtp":     96.23,
			}, {
				"cost":    500,
				"id":      1,
				"prefix2": "_BASE_FG_HOT",
				"rtp":     96.28,
			}, {
				"cost":    9,
				"id":      2,
				"prefix2": "_BASE_GOLD_FLOW_MIX",
				"rtp":     96.27,
			}, {
				"cost":    180,
				"id":      3,
				"prefix2": "_BASE_FG_MIX",
				"rtp":     96.26,
			}},
			"selectId": -1,
			"wasBuy":   0,
		},
		"denomAssortment_cents": []int{1},
		"dop": map[string]any{
			"base_sc":    0,
			"hi":         4,
			"lasthi":     4,
			"nextfghi":   4,
			"nextfgmult": 1,
			"storageN":   0,
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "FUN",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{[]any{"off", 0, 0}},
			"fg": map[string]any{
				"firstAward":   []int{0, 0, 0, 0, 8, 10, 12, 0},
				"limit":        50,
				"nestingAward": 3,
				"portions":     3,
			},
			"paytable": []any{[]any{[]int{0, 1}}, []any{[]int{1, 256}, []int{20, 2500}, []int{15, 2000}, []int{10, 1500}, []int{8, 1000}, []int{7, 500}, []int{6, 300}, []int{5, 200}, []int{4, 100}, []int{3, 50}}, []any{[]int{2, 256}, []int{20, 1500}, []int{15, 1000}, []int{10, 500}, []int{8, 300}, []int{7, 250}, []int{6, 200}, []int{5, 100}, []int{4, 50}, []int{3, 30}}, []any{[]int{3, 256}, []int{20, 1000}, []int{15, 500}, []int{10, 300}, []int{8, 250}, []int{7, 200}, []int{6, 100}, []int{5, 50}, []int{4, 30}, []int{3, 20}}, []any{[]int{4, 256}, []int{20, 500}, []int{15, 300}, []int{10, 250}, []int{8, 200}, []int{7, 100}, []int{6, 50}, []int{5, 30}, []int{4, 20}, []int{3, 10}}, []any{[]int{5, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{6, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{7, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{8, 256}, []int{20, 100}, []int{15, 50}, []int{10, 40}, []int{8, 30}, []int{7, 20}, []int{6, 15}, []int{5, 10}, []int{4, 5}, []int{3, 3}}, []any{[]int{9, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{10, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{11, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{12, 256}, []int{20, 50}, []int{15, 30}, []int{10, 20}, []int{8, 15}, []int{7, 10}, []int{6, 5}, []int{5, 3}, []int{4, 2}, []int{3, 1}}, []any{[]int{13, 8}}, []any{[]int{14, 32}}, []any{[]int{15, 16}}, []any{[]int{16, 1}}, []any{[]int{17, 32}}, []any{[]int{18, 256}, []int{0, 0}}, []any{[]int{19, 256}, []int{0, 0}}, []any{[]int{20, 256}, []int{0, 0}}, []any{[]int{21, 256}, []int{0, 0}}, []any{[]int{22, 256}, []int{0, 0}}, []any{[]int{23, 256}, []int{0, 0}}, []any{[]int{24, 256}, []int{0, 0}}, []any{[]int{25, 256}, []int{0, 0}}, []any{[]int{26, 256}, []int{0, 0}}, []any{[]int{27, 256}, []int{0, 0}}},
		},
		"helpseed":                true,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"last_markbox":            []any{[]int{0, 0, 1, 1, 1, 0}, []int{0, 0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 2, 2}, []int{0, 0, 0, 0, 2, 2}},
		"last_resbox":             []any{[]int{1, 1, 302, -2, -2, 3}, []int{10, 1, 5, 3, 3, 3}, []int{1, 10, 8, 8, 600, 0}, []int{8, 9, 9, 9, 0, 0}},
		"linesAssortment":         []int{20},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   5000,
		"minBetPerGame_cents":     nil,
		"mylinesInfo_v2":          []any{},
		"nlines":                  20,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               ctx["input"],
		"present":                 "no",
		"reelstate":               0,
		"setVip_inFreeSpinAlways": -1,
		"startBox":                []any{[]int{1, 1, 302, -2, -2, 3}, []int{10, 1, 5, 3, 3, 3}, []int{1, 10, 8, 8, 600, 0}, []int{8, 9, 9, 9, 0, 0}},
		"start_markbox":           []any{[]int{0, 0, 1, 1, 1, 0}, []int{0, 0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 2, 2}, []int{0, 0, 0, 0, 2, 2}},
		"stopBox":                 []any{[]int{1, 1, 302, -2, -2, 3}, []int{10, 1, 5, 3, 3, 3}, []int{1, 10, 8, 8, 600, 0}, []int{8, 9, 9, 9, 0, 0}},
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.25,
			"vip_noSpecSeed": true,
			"wasBuyVip":      0,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         25000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 25000,
		},
		"info_adv_games": map[string]any{
			"hint": 0,
			"list": []any{},
			"nnew": 0,
		},
		"newOpenGames": []any{},
		"ss":           map[string]any{},
		"useracc": map[string]any{
			"altcurr":      "fun",
			"amount":       100000,
			"currency":     "FUN",
			"currencyType": 2,
			"currencyUnit": 1,
			"symbol_first": "0",
		},
		"uservars": map[string]any{
			"language": "en",
		},
	}

	b, _ := json.Marshal(gs)
	return string(b)
}

func (m m400159) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	case 2:
		return 50000
	case 3:
		return 900
	case 4:
		return 18000
	case 5:
		return 125
	default:
		return 100
	}
}

func (m m400159) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}

func (m m400159) makeGrid(rows int, cols int) [][]int16 {
	gridRows := make([][]int16, rows)
	for row := 0; row < rows; row++ {
		gridRows[row] = make([]int16, cols)
		for col := 0; col < cols; col++ {
			gridRows[row][col] = -99
		}
	}
	return gridRows
}

func (m m400159) cloneSlice400159(src [][]int16) [][]int16 {
	dst := make([][]int16, len(src))
	for i := range src {
		dst[i] = make([]int16, len(src[i]))
		copy(dst[i], src[i])
	}
	return dst
}

func (m *m400159) genNormAllGrid(stopGrid [][]int16, row int, wins []games.Win400159) ([][]int16, [][]int16, [][]int16) {
	cleanGrid := m.cloneSlice400159(stopGrid)
	newStopGrid := m.makeGrid(row, m.Config.Column)
	dropShiftGrid := m.makeGrid(row, m.Config.Column)
	for _, v := range wins {
		for _, pos := range v.Positions {
			cleanGrid[pos.Row][pos.Col] = -99
		}
	}
	winMaskGrid := m.cloneSlice400159(cleanGrid)
	for i := 0; i < row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			if winMaskGrid[i][j] == -99 {
				winMaskGrid[i][j] = 1
			} else {
				winMaskGrid[i][j] = 0
			}
		}
	}
	for i := 0; i < m.Config.Column; i++ {
		var result []int16
		for j := row - 1; j > 0; j-- {
			if cleanGrid[j][i] != -99 {
				result = append(result, cleanGrid[j][i])
			}
		}
		// 从底部开始填充非-99的值，顶部填充-99
		fillIndex := row - 1 // 从最底部开始填充
		for _, val := range result {
			if fillIndex > 0 {
				newStopGrid[fillIndex][i] = val
				fillIndex--
			}
		}
	}
	newPosGrid := m.cloneSlice400159(newStopGrid)
	for i := 0; i < row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			if newPosGrid[i][j] == -99 {
				winMaskGrid[i][j] = 1
			} else {
				winMaskGrid[i][j] = 0
			}
		}
	}
	count := make([]int, m.Config.Column)
	for i := 0; i < row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			if winMaskGrid[i][j] == newPosGrid[i][j] {
				count[j]++
			}
		}
	}
	for i := 0; i < m.Config.Column; i++ {
		for j := row - 1; j > 0; j-- {
			if count[i] > 0 {
				dropShiftGrid[j][i] = 1
				count[i]--
			}
		}
	}
	return winMaskGrid, newPosGrid, dropShiftGrid
}
