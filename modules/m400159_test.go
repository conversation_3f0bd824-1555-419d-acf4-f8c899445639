package modules

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
)

func TestM400159(t *testing.T) {
	// 加载配置
	config, _ := os.ReadFile("../bin/configs/400159.yaml")
	m := m400159{}
	m.Init(config)
	// 生成默认棋盘
	rd := rand.New(rand.NewSource(4))
	grid := make([][]int16, m.Config.InitRow)
	for i := 0; i < m.Config.InitRow; i++ {
		grid[i] = m.RandByWeight.More(m.Config.Column, rd)
	}
	// 基础棋盘 4*6
	for row := range grid {
		fmt.Println(grid[row])
	}

	fmt.Println()
	mask := checkLine(&m, grid)
	for row := range mask {
		fmt.Println(mask[row])
	}

	fmt.Println()
	newGrid := dropSymbols(&m, rd, grid, mask)
	for row := range newGrid {
		fmt.Println(newGrid[row])
	}
	// 每触发一次消除则递归一次,叠加棋盘行数
}

func addNewRow(m *m400159, rd *rand.Rand, grid [][]int16) [][]int16 {
	if len(grid) == 0 {
		return [][]int16{{0, 0, 0, 0, 0, 0}}
	}
	if len(grid) >= m.Config.MaxRow {
		return grid
	}
	newGrid := make([][]int16, 0, len(grid)+1)
	newGrid = append(newGrid, m.RandByWeight.More(m.Config.Column, rd))
	newGrid = append(newGrid, grid...)
	return newGrid
}

func checkLine(m *m400159, grid [][]int16) [][]int16 {
	mask := make([][]int16, len(grid))
	firstIco := []int16{}
	for i := range mask {
		mask[i] = make([]int16, m.Config.Column)
		firstIco = append(firstIco, grid[i][0])
	}

	// 对每个图标进行检查
	for _, ico := range firstIco {
		consecutiveCount := 0

		for col := 0; col < m.Config.Column; col++ {
			hasIconInCol := false
			for row := 0; row < len(grid); row++ {
				if grid[row][col] == ico || grid[row][col] == m.Config.WildIcon {
					hasIconInCol = true
					break
				}
			}

			if hasIconInCol {
				consecutiveCount++
			} else {
				break
			}
		}

		if consecutiveCount >= 3 {
			for row := 0; row < len(grid); row++ {
				for col := 0; col < m.Config.Column; col++ {
					if grid[row][col] == ico || grid[row][col] == m.Config.WildIcon {
						mask[row][col] = 1
					}
				}
			}
		}
	}

	return mask
}

func dropSymbols(m *m400159, rd *rand.Rand, grid [][]int16, mask [][]int16) [][]int16 {

}
