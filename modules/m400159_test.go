package modules

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
)

func TestM400159(t *testing.T) {
	// 加载配置
	config, _ := os.ReadFile("../bin/configs/400159.yaml")
	m := m400159{}
	m.Init(config)
	// 生成默认棋盘
	rd := rand.New(rand.NewSource(3))
	grid := make([][]int16, m.Config.InitRow)
	for i := 0; i < m.Config.InitRow; i++ {
		grid[i] = m.RandByWeight.More(m.Config.Column, rd)
	}
	// 基础棋盘 4*6
	for row := range grid {
		fmt.Println(grid[row])
	}
	fmt.Println()
	mask := checkLine(&m, grid)
	for row := range mask {
		fmt.Println(mask[row])
	}
	// newGrid := addNewRow(&m, rd, grid)
	// for row := range newGrid {
	// 	fmt.Println(newGrid[row])
	// }
	// 每触发一次消除则递归一次,叠加棋盘行数
}

func addNewRow(m *m400159, rd *rand.Rand, grid [][]int16) [][]int16 {
	if len(grid) == 0 {
		return [][]int16{{0, 0, 0, 0, 0, 0}}
	}
	if len(grid) >= m.Config.MaxRow {
		return grid
	}
	newGrid := make([][]int16, 0, len(grid)+1)
	newGrid = append(newGrid, m.RandByWeight.More(m.Config.Column, rd))
	newGrid = append(newGrid, grid...)
	return newGrid
}

func checkLine(m *m400159, grid [][]int16) [][]int16 {
	mask := make([][]int16, len(grid))
	firstIcons := []int16{}
	for i := range mask {
		mask[i] = make([]int16, m.Config.Column)
		firstIcons = append(firstIcons, grid[i][0])
	}

	for _, ico := range firstIcons {

		for row := 0; row < len(grid); row++ {
			if grid[row][0] != ico {
				continue
			}
			start
			for col := 0; col < m.Config.Column; col++ {
				for r := 0; r < len(grid); r++ {
					if grid[r][col] == ico {
						mask[r][col] = 1
					}
				}
			}
		}
	}
	return mask
}
