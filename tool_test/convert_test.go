package test

import (
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"testing"
)

// JSONToGoMapString 将JSON字符串转换为Go语言map[string]any{}格式的字符串
func JSONToGoMapString(jsonStr string) (string, error) {
	var data any
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", fmt.Errorf("解析JSON失败: %v", err)
	}

	return convertToGoMapString(data, 0), nil
}

// convertToGoMapString 递归转换任意类型为Go map字符串格式
func convertToGoMapString(data any, indent int) string {
	if data == nil {
		return "nil"
	}

	v := reflect.ValueOf(data)
	switch v.Kind() {
	case reflect.Map:
		return convertMapToGoString(data.(map[string]any), indent)
	case reflect.Slice:
		return convertSliceToGoString(data.([]any), indent)
	case reflect.String:
		return fmt.Sprintf(`"%s"`, data.(string))
	case reflect.Bool:
		return fmt.Sprintf("%t", data.(bool))
	case reflect.Float64:
		// JSON数字默认解析为float64
		f := data.(float64)
		if f == float64(int64(f)) {
			return fmt.Sprintf("%d", int64(f))
		}
		return fmt.Sprintf("%g", f)
	default:
		return fmt.Sprintf("%v", data)
	}
}

// convertMapToGoString 转换map为Go字符串格式
func convertMapToGoString(m map[string]any, indent int) string {
	if len(m) == 0 {
		return "map[string]any{}"
	}

	var builder strings.Builder
	builder.WriteString("map[string]any{\n")

	// 对键进行排序以保证输出一致性
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, key := range keys {
		value := m[key]
		indentStr := strings.Repeat("\t", indent+1)
		builder.WriteString(fmt.Sprintf(`%s"%s": %s,`+"\n", indentStr, key, convertToGoMapString(value, indent+1)))
	}

	builder.WriteString(strings.Repeat("\t", indent) + "}")
	return builder.String()
}

// convertSliceToGoString 转换slice为Go字符串格式
func convertSliceToGoString(s []any, indent int) string {
	if len(s) == 0 {
		return "[]any{}"
	}

	// 检查slice中所有元素的类型以确定最合适的类型
	sliceType := determineSliceType(s)

	var builder strings.Builder
	builder.WriteString(sliceType + "{")

	// 如果是简单类型且元素不多，可以放在一行
	if isSimpleSlice(s) && len(s) <= 10 {
		for i, item := range s {
			if i > 0 {
				builder.WriteString(", ")
			}
			builder.WriteString(convertToGoMapString(item, indent))
		}
	} else {
		// 复杂类型或元素较多，分行显示
		// builder.WriteString("\n")
		for _, item := range s {
			// indentStr := strings.Repeat("\t", indent+1)
			builder.WriteString(fmt.Sprintf("%s%s,", "", convertToGoMapString(item, indent+1)))
		}
		// builder.WriteString(strings.Repeat("\t", indent))
	}

	builder.WriteString("}")
	return builder.String()
}

// determineSliceType 确定slice的Go类型
func determineSliceType(s []any) string {
	if len(s) == 0 {
		return "[]any"
	}

	// 检查是否所有元素都是同一类型
	firstType := reflect.TypeOf(s[0])
	allSameType := true

	for _, item := range s {
		if reflect.TypeOf(item) != firstType {
			allSameType = false
			break
		}
	}

	if !allSameType {
		return "[]any"
	}

	// 根据第一个元素的类型确定slice类型
	switch s[0].(type) {
	case string:
		return "[]string"
	case bool:
		return "[]bool"
	case float64:
		// 检查是否都是整数
		allInts := true
		for _, item := range s {
			if f, ok := item.(float64); ok {
				if f != float64(int64(f)) {
					allInts = false
					break
				}
			}
		}
		if allInts {
			return "[]int"
		}
		return "[]float64"
	case map[string]any:
		return "[]map[string]any"
	default:
		return "[]any"
	}
}

// isSimpleSlice 判断是否为简单类型的slice
func isSimpleSlice(s []any) bool {
	for _, item := range s {
		switch item.(type) {
		case map[string]any, []any:
			return false
		}
	}
	return true
}

// TestJSONToGoMapString 测试JSON转换为Go map字符串的功能
func TestJSONToGoMapString(t *testing.T) {
	// 测试用例1：简单对象
	jsonStr1 := `{
        "gamegroup": "base",
        "doubleAssortment": ["off"],
        "maxBetPerGame_cents": null,
        "betAssortment": [
            1, 2, 3, 4, 5, 6, 7, 10, 15, 20, 25, 30, 35, 40, 45, 50, 60, 75, 100
        ],
        "denomAssortment_cents": [1],
        "minBetPerGame_cents": null,
        "winValidation": {
            "needcheck": false,
            "winlimit_fictiveRotate_gcurr": 25000000,
            "remaintime": 86400000,
            "period": 86400000,
            "isApproved": false,
            "isNotApproved": false,
            "isWaitApprove": false
        },
        "buyBonus": {
            "wasBuy": 0,
            "selectId": -1,
            "buyTotalBetK": [
                {
                    "id": 0,
                    "cost": 100,
                    "prefix2": "_BASE_FG",
                    "rtp": 96.24
                },
                {
                    "id": 1,
                    "cost": 600,
                    "prefix2": "_BASE_FG_HOT",
                    "rtp": 96.28
                },
                {
                    "id": 2,
                    "cost": 125,
                    "prefix2": "_BASE_FG_MIX_0_20",
                    "rtp": 96.25
                },
                {
                    "id": 3,
                    "cost": 200,
                    "prefix2": "_BASE_FG_MIX_1_5",
                    "rtp": 96.26
                },
                {
                    "id": 4,
                    "cost": 350,
                    "prefix2": "_BASE_FG_MIX_2_2",
                    "rtp": 96.27
                }
            ]
        },
        "outRatesVolatility": null,
        "placedbet": 20,
        "gcurrency": "FUN",
        "gdenom": 1,
        "present": "no",
        "betPerGame": 20,
        "betPerLine": 1,
        "nlines": 20,
        "phaseCur": "finished",
        "phaseNext": "toIdle",
        "maxBetPerGame_credits": 2000,
        "analInfo": {
            "formula": {
                "args": ["betPerLine", "nlines"],
                "body": "return(betPerLine * nlines/1)"
            },
            "formulaReverse": {
                "args": ["betPerGame", "nlines"],
                "body": "return(betPerGame / nlines*1)"
            },
            "lineStyles": null,
            "symbolNames": [
                "senior",
                "middle",
                "junior",
                "young",
                "chicken",
                "lobster",
                "meat",
                "fish",
                "egg",
                "hearts",
                "spades",
                "diamonds",
                "clubs",
                "scatter",
                "hearts",
                "hearts",
                "2x",
                "3x",
                "4x",
                "5x",
                "6x",
                "8x",
                "10x",
                "12x",
                "15x",
                "20x",
                "25x",
                "50x",
                "100x",
                "250x",
                "spades",
                "2x",
                "2x",
                "2x",
                "2x",
                "2x",
                "2x"
            ],
            "baseReels": [
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ]
            ],
            "freeReels": [
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ],
                [
                    9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12, 9, 10, 11, 12,
                    9, 10, 11, 12, 9, 10, 11, 12
                ]
            ],
            "statTablo": {
                "volatility": 10,
                "bigwin": 10,
                "epicwin": 10,
                "bonus": 5,
                "show": 1,
                "rtp": 96.14
            },
            "maxWinFreq_small": 300558,
            "maxWinFreq_big": 81789076,
            "VIP_maxWinFreq_small": 180265,
            "VIP_maxWinFreq_big": 5322317,
            "arrlimits_winLimitK": [5000, 12000],
            "scatterIds": [13],
            "minScatters": [4, 4],
            "nSmallPicts": 13,
            "maxIteration": 10,
            "outRates_vipmode": 96.22,
            "nhot_sectors": [1, 4, 10],
            "n_sectors": 20,
            "volatility": 4,
            "sasAdditionalId": "HOB",
            "sasPaytableId": "HOB960"
        },
        "helpInfo": {
            "paytable": [
                [
                    [0, 256],
                    [12, 1000],
                    [10, 500],
                    [8, 200]
                ],
                [
                    [1, 256],
                    [12, 500],
                    [10, 200],
                    [8, 50]
                ],
                [
                    [2, 256],
                    [12, 300],
                    [10, 100],
                    [8, 40]
                ],
                [
                    [3, 256],
                    [12, 240],
                    [10, 40],
                    [8, 30]
                ],
                [
                    [4, 256],
                    [12, 200],
                    [10, 30],
                    [8, 20]
                ],
                [
                    [5, 256],
                    [12, 160],
                    [10, 25],
                    [8, 16]
                ],
                [
                    [6, 256],
                    [12, 100],
                    [10, 20],
                    [8, 10]
                ],
                [
                    [7, 256],
                    [12, 80],
                    [10, 18],
                    [8, 8]
                ],
                [
                    [8, 256],
                    [12, 40],
                    [10, 15],
                    [8, 5]
                ],
                [
                    [9, 256],
                    [12, 10],
                    [10, 5],
                    [8, 1]
                ],
                [
                    [10, 256],
                    [12, 10],
                    [10, 5],
                    [8, 1]
                ],
                [
                    [11, 256],
                    [12, 10],
                    [10, 5],
                    [8, 1]
                ],
                [
                    [12, 256],
                    [12, 10],
                    [10, 5],
                    [8, 1]
                ],
                [[13, 8]],
                [[14, 16]],
                [[15, 16]],
                [
                    [16, 128],
                    [1, 2]
                ],
                [
                    [17, 128],
                    [1, 3]
                ],
                [
                    [18, 128],
                    [1, 4]
                ],
                [
                    [19, 128],
                    [1, 5]
                ],
                [
                    [20, 128],
                    [1, 6]
                ],
                [
                    [21, 128],
                    [1, 8]
                ],
                [
                    [22, 128],
                    [1, 10]
                ],
                [
                    [23, 128],
                    [1, 12]
                ],
                [
                    [24, 128],
                    [1, 15]
                ],
                [
                    [25, 128],
                    [1, 20]
                ],
                [
                    [26, 128],
                    [1, 25]
                ],
                [
                    [27, 128],
                    [1, 50]
                ],
                [
                    [28, 128],
                    [1, 100]
                ],
                [
                    [29, 128],
                    [1, 250]
                ],
                [[30, 32]]
            ],
            "fg": {
                "firstAward": 10,
                "nestingAward": 5,
                "limit": 100,
                "portions": 5
            },
            "doubles": [["off", 0, 0]]
        },
        "doubleActive": "off",
        "doubleActiveDbSettings": "off",
        "antiDynamiteBet": null,
        "dramshow": null,
        "versions": {
            "server_core": "1.1",
            "server_game": "1.0",
            "server_math": "1.0"
        },
        "winlimits": {
            "maxWinLimitK": 12000,
            "maxWin_gcurr": null,
            "needControlJackpot": false,
            "winLimitK_gameconfig": 12000
        },
        "isMaxFlag": 0,
        "isMaxFlag_lines": 0,
        "linesAssortment": [20],
        "linesPerCredit": 1,
        "reelstate": 0,
        "aux": 0,
        "startBox": [
            [8, 1, 2, 13, 1, 10],
            [8, 12, 13, 11, 12, 11],
            [8, 9, 9, 10, 12, 9],
            [1, 13, 9, 1, 12, 2],
            [1, 11, 9, 1, 9, 2]
        ],
        "stopBox": [
            [8, 1, 2, 13, 1, 10],
            [8, 12, 13, 11, 12, 11],
            [8, 9, 9, 10, 12, 9],
            [1, 13, 9, 1, 12, 2],
            [1, 11, 9, 1, 9, 2]
        ],
        "incutId": 9,
        "vipMode": {
            "on": 0,
            "vipBetK": 1.25,
            "wasBuyVip": 0,
            "vip_noSpecSeed": true
        },
        "setVip_inFreeSpinAlways": -1,
        "bbLimitsWinK": [12000, 12000, 12000, 12000, 12000],
        "helpseed": true,
        "iBarrel": [0],
        "iBarrelPos": [0],
        "flyBarrels": [],
        "sMult": 1,
        "fMult": 1
    }`

	result1, err := JSONToGoMapString(jsonStr1)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例1：简单对象 ===")
	fmt.Println(result1)
	fmt.Println()

	// 测试用例2：复杂嵌套对象
	jsonStr2 := `{
		"winValidation": {
			"needcheck": false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime": 86400000,
			"period": 86400000,
			"isApproved": false,
			"isNotApproved": false,
			"isWaitApprove": false
		},
		"buyBonus": {
			"wasBuy": 0,
			"selectId": -1,
			"buyTotalBetK": [
				{"id": 0, "cost": 100, "prefix2": "_BASE_FG", "rtp": 96.23},
				{"id": 1, "cost": 500, "prefix2": "_BASE_FG_HOT", "rtp": 96.25}
			]
		}
	}`

	result2, err := JSONToGoMapString(jsonStr2)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例2：复杂嵌套对象 ===")
	fmt.Println(result2)
	fmt.Println()

	// 测试用例3：二维数组
	jsonStr3 := `{
		"startBox": [
			[8, 1, 1, 10, 11],
			[8, 8, 3, 10, 4],
			[5, 4, 8, 10, 8]
		],
		"paytable": [
			[[0, 8]],
			[[1, 1]],
			[[2, 8]]
		]
	}`

	result3, err := JSONToGoMapString(jsonStr3)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例3：二维数组 ===")
	fmt.Println(result3)
	fmt.Println()
}

// ExampleJSONToGoMapString 示例使用函数
func TestExampleJSONToGoMapString(t *testing.T) {
	// 示例JSON字符串
	jsonStr := `{
		"gamegroup": "base",
		"doubleAssortment": ["off"],
		"maxBetPerGame_cents": null,
		"betAssortment": [1, 2, 3, 4, 5, 8, 10, 15, 20, 25, 50, 75, 100],
		"winValidation": {
			"needcheck": false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime": 86400000
		},
		"startBox": [
			[8, 1, 1, 10, 11],
			[8, 8, 3, 10, 4],
			[5, 4, 8, 10, 8]
		]
	}`

	// 转换为Go map字符串格式
	result, err := JSONToGoMapString(jsonStr)
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return
	}

	fmt.Println("转换结果:")
	fmt.Println(result)
}
